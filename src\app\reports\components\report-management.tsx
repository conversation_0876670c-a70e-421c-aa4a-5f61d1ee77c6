'use client';

import {
  FileText,
  BarChart3,
  TrendingUp,
  DollarSign,
  Clock,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

import { DashboardHeader } from '../../dashboard/components/dashboard-header';

export function ReportManagement() {
  const [reportType, setReportType] = useState('');

  const reportTypes = [
    {
      id: 'cases-summary',
      name: 'Resumen de Casos',
      description: 'Reporte general de casos por período',
      category: 'Casos',
    },
    {
      id: 'creditors-analysis',
      name: '<PERSON><PERSON><PERSON><PERSON> de Acreedores',
      description: 'Estadísticas de acreedores y recuperación',
      category: 'Acreedores',
    },
    {
      id: 'agreements-report',
      name: '<PERSON>e de Acuerdos',
      description: 'Acuerdos exitosos y cumplimiento',
      category: '<PERSON>cuerdo<PERSON>',
    },
    {
      id: 'financial-analysis',
      name: '<PERSON><PERSON><PERSON><PERSON>',
      description: '<PERSON><PERSON>, recuperación y eficiencia',
      category: 'Financiero',
    },
    {
      id: 'operator-performance',
      name: 'Rendimiento de Operadores',
      description: 'Productividad y eficiencia por operador',
      category: 'Operadores',
    },
    {
      id: 'legal-compliance',
      name: 'Cumplimiento Legal',
      description: 'Cumplimiento de términos y procedimientos',
      category: 'Legal',
    },
  ];

  const quickStats = [
    {
      title: 'Casos Este Mes',
      value: '24',
      change: '+12%',
      icon: FileText,
      color: 'text-blue-600',
    },
    {
      title: 'Acuerdos Exitosos',
      value: '18',
      change: '+8%',
      icon: TrendingUp,
      color: 'text-green-600',
    },
    {
      title: 'Monto Recuperado',
      value: '$450M',
      change: '+15%',
      icon: DollarSign,
      color: 'text-purple-600',
    },
    {
      title: 'Tiempo Promedio',
      value: '45 días',
      change: '-5%',
      icon: Clock,
      color: 'text-orange-600',
    },
  ];

  const generateReport = () => {
    if (!reportType) {
      alert('Por favor seleccione un tipo de reporte');
      return;
    }

    const reportName = reportTypes.find((r) => r.id === reportType)?.name;
    const message = `Generando reporte: ${reportName}`;
    toast.success(message);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                Reportes y Estadísticas
              </h1>
              <p className="text-gray-600">
                Genere reportes y analice estadísticas del sistema
              </p>
            </div>
            <Button onClick={generateReport}>
              <BarChart3 className="mr-2 h-4 w-4" />
              Generar Reporte
            </Button>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            {quickStats.map((stat, index) => (
              <Card key={index}>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">
                        {stat.title}
                      </p>
                      <p className="text-2xl font-bold">{stat.value}</p>
                      <p
                        className={`text-sm ${
                          stat.change.startsWith('+')
                            ? 'text-green-600'
                            : 'text-red-600'
                        }`}
                      >
                        {stat.change} vs mes anterior
                      </p>
                    </div>
                    <stat.icon className={`h-8 w-8 ${stat.color}`} />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tipo de Reporte</TableHead>
                <TableHead>Descripción</TableHead>
                <TableHead>Categoría</TableHead>
                <TableHead>Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {reportTypes.map((type) => (
                <TableRow key={type.id}>
                  <TableCell>{type.name}</TableCell>
                  <TableCell>{type.description}</TableCell>
                  <TableCell>{type.category}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => setReportType(type.id)}
                    >
                      <BarChart3 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </main>
    </div>
  );
}
