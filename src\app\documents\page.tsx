import { getDocuments, getDocumentStats } from '@/features/document/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';

import { DocumentsPageClient } from './documents-page-client';

export default async function DocumentsPage() {
  const [documentsResult, statsResult] = await Promise.all([
    getDocuments({}),
    getDocumentStats(),
  ]);

  const documentsData = documentsResult[0] || [];
  const statsData = statsResult[0] || { total: 0, byStatus: [], byType: [] };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <DocumentsPageClient
          initialDocuments={documentsData}
          initialStats={statsData}
        />
      </main>
    </div>
  );
}
