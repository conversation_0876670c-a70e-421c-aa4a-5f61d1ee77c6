import { z } from 'zod';

const mapDocumentTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    CEDULA: 'Cédula',
    RUT: 'RUT',
    ESTADOS_FINANCIEROS: 'Estados Financieros',
    CERTIFICADO_INGRESOS: 'Certificado de Ingresos',
    ESCRITURA_PUBLICA: 'Escritura Pública',
    AUTORIZACION: 'Autorización',
    DEMANDA: 'Demanda',
    RESPUESTA_DEMANDA: 'Respuesta a Demanda',
    ACUERDO: 'Acuerdo',
    SENTENCIA: 'Sentencia',
    OTRO: 'Otro',
  };
  return typeMap[type] || type;
});

const mapDocumentStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    PENDIENTE: 'Pendiente',
    APROBADO: 'Aprobado',
    RECHAZADO: 'Rechazado',
    EN_REVISION: 'En Revisión',
  };
  return statusMap[status] || status;
});

const mapCaseTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
});

const mapCaseStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: 'Cerrado',
  };
  return statusMap[status] || status;
});

const caseForDocumentSchema = z.object({
  id: z.string(),
  caseNumber: z.string(),
  debtorName: z.string(),
  type: mapCaseTypeToSpanish,
  status: mapCaseStatusToSpanish,
});

export const documentWithCaseSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: mapDocumentTypeToSpanish,
  status: mapDocumentStatusToSpanish,
  url: z.string(),
  uploadDate: z.coerce.date(),
  caseId: z.string(),
  case: caseForDocumentSchema,
});

export type DocumentWithCase = z.infer<typeof documentWithCaseSchema>;

export const documentSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  status: z.string(),
  url: z.string().optional(),
  uploadDate: z.coerce.date().optional(),
  caseId: z.string().optional(),
  case: caseForDocumentSchema.optional(),
  debtorName: z.string().optional(),
  createdDate: z.string().optional(),
  size: z.string().optional(),
  format: z.string().optional(),
  createdBy: z.string().optional(),
  downloadCount: z.coerce.number().optional(),
  lastAccessed: z.string().optional(),
  viewCount: z.coerce.number().optional(),
  shareCount: z.coerce.number().optional(),
});

export type Document = z.infer<typeof documentSchema>;

export const documentFilterSchema = z.object({
  caseId: z.string().optional(),
  type: z.string().optional(),
  status: z.string().optional(),
  search: z.string().optional(),
});

export type DocumentFilter = z.infer<typeof documentFilterSchema>;

export const documentStatsSchema = z.object({
  total: z.coerce.number(),
  byStatus: z.array(
    z.object({
      status: z.string(),
      _count: z.object({
        id: z.coerce.number(),
      }),
    }),
  ),
  byType: z.array(
    z.object({
      type: z.string(),
      _count: z.object({
        id: z.coerce.number(),
      }),
    }),
  ),
});

export type DocumentStats = z.infer<typeof documentStatsSchema>;

export const createDocumentSchema = z.object({
  name: z.string().min(1, 'El nombre es requerido'),
  type: z.string().min(1, 'El tipo es requerido'),
  status: z.string().optional().default('PENDIENTE'),
  url: z.string().url('URL inválida'),
  caseId: z.string().min(1, 'El ID del caso es requerido'),
});

export type CreateDocumentData = z.infer<typeof createDocumentSchema>;

export const updateDocumentSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
  name: z.string().min(1, 'El nombre es requerido').optional(),
  type: z.string().min(1, 'El tipo es requerido').optional(),
  status: z.string().optional(),
  url: z.string().url('URL inválida').optional(),
  caseId: z.string().min(1, 'El ID del caso es requerido').optional(),
});

export type UpdateDocumentData = z.infer<typeof updateDocumentSchema>;

export const deleteDocumentSchema = z.object({
  id: z.string().min(1, 'El ID es requerido'),
});

export type DeleteDocumentData = z.infer<typeof deleteDocumentSchema>;

// Esquemas para plantillas de documentos
export const documentTemplateSchema = z.object({
  id: z.string(),
  googleDriveId: z.string(),
  fileName: z.string(),
  mimeType: z.string(),
  placeholders: z.array(
    z.object({
      key: z.string(),
      label: z.string(),
      type: z.enum(['text', 'number', 'date', 'boolean']),
      required: z.boolean().optional().default(false),
      defaultValue: z.string().optional(),
      description: z.string().optional(),
    }),
  ),
  createdAt: z.coerce.date(),
  updatedAt: z.coerce.date(),
});

export type DocumentTemplate = z.infer<typeof documentTemplateSchema>;

export const editDocumentTemplateSchema = z.object({
  placeholders: z
    .array(
      z.object({
        key: z.string().min(1, 'La clave es requerida'),
        label: z.string().min(1, 'La etiqueta es requerida'),
        type: z.enum(['text', 'number', 'date', 'boolean']),
        required: z.boolean().optional().default(false),
        defaultValue: z.string().optional(),
        description: z.string().optional(),
      }),
    )
    .optional()
    .default([]),
});

export type EditDocumentTemplateData = z.infer<
  typeof editDocumentTemplateSchema
>;

export const generateDocumentFromTemplateSchema = z.object({
  templateId: z.string().min(1, 'El ID de la plantilla es requerido'),
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  name: z.string().min(1, 'El nombre del documento es requerido'),
  placeholderValues: z.record(z.string(), z.any()),
});

export type GenerateDocumentFromTemplateData = z.infer<
  typeof generateDocumentFromTemplateSchema
>;

// Esquema para actualización de contenido de documentos
export const updateDocumentContentSchema = z.object({
  documentId: z.string().min(1, 'El ID del documento es requerido'),
  content: z.string().min(1, 'El contenido es requerido'),
});

export type UpdateDocumentContentData = z.infer<
  typeof updateDocumentContentSchema
>;

export const getDocumentsSchema = z.array(documentWithCaseSchema);
export const getDocumentByIdSchema = documentWithCaseSchema;
export const createDocumentOutputSchema = documentWithCaseSchema;
export const updateDocumentOutputSchema = documentWithCaseSchema;
export const deleteDocumentOutputSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  status: z.string(),
  url: z.string(),
  uploadDate: z.coerce.date(),
  caseId: z.string(),
});
