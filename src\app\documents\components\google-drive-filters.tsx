'use client';

import { useState, useEffect } from 'react';
import { useServerAction } from 'zsa-react';
import { Folder, File, ChevronRight, ChevronDown, Loader2 } from 'lucide-react';

import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { getGoogleDriveFoldersAndFiles } from '@/features/document/actions';
import type {
  GoogleDriveFolder,
  GoogleDriveFile,
} from '@/features/document/types';

interface GoogleDriveFiltersProps {
  onFolderSelect?: (folderId: string, folderName: string) => void;
  onFileSelect?: (fileId: string, fileName: string) => void;
  selectedFolderId?: string;
  selectedFileId?: string;
}

export function GoogleDriveFilters({
  onFolderSelect,
  onFileSelect,
  selectedFolderId,
  selectedFileId,
}: GoogleDriveFiltersProps) {
  const [folders, setFolders] = useState<GoogleDriveFolder[]>([]);
  const [files, setFiles] = useState<GoogleDriveFile[]>([]);
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(
    new Set(),
  );
  const [folderContents, setFolderContents] = useState<
    Map<string, { folders: GoogleDriveFolder[]; files: GoogleDriveFile[] }>
  >(new Map());

  const { execute: loadFoldersAndFiles, isPending } = useServerAction(
    getGoogleDriveFoldersAndFiles,
    {
      onSuccess: ({ data }) => {
        setFolders(data.folders);
        setFiles(data.files);
      },
      onError: ({ err }) => {
        console.error('Error cargando contenido de Google Drive:', err);
      },
    },
  );

  const { execute: loadSubfolderContent } = useServerAction(
    getGoogleDriveFoldersAndFiles,
    {
      onSuccess: ({ data }, { input }) => {
        if (input?.folderId) {
          setFolderContents((prev) => new Map(prev).set(input.folderId, data));
        }
      },
    },
  );

  useEffect(() => {
    loadFoldersAndFiles();
  }, [loadFoldersAndFiles]);

  const toggleFolder = (folderId: string) => {
    const newExpanded = new Set(expandedFolders);
    if (newExpanded.has(folderId)) {
      newExpanded.delete(folderId);
    } else {
      newExpanded.add(folderId);
      // Load subfolder content if not already loaded
      if (!folderContents.has(folderId)) {
        loadSubfolderContent({ folderId });
      }
    }
    setExpandedFolders(newExpanded);
  };

  const handleFolderClick = (folderId: string, folderName: string) => {
    onFolderSelect?.(folderId, folderName);
  };

  const handleFileClick = (fileId: string, fileName: string) => {
    onFileSelect?.(fileId, fileName);
  };

  const getFileIcon = (mimeType?: string) => {
    if (mimeType?.includes('word') || mimeType?.includes('document')) {
      return <File className="h-4 w-4 text-blue-600" />;
    }
    return <File className="h-4 w-4 text-gray-600" />;
  };

  const getFileTypeLabel = (mimeType?: string) => {
    if (mimeType?.includes('word')) return 'Word';
    if (mimeType?.includes('pdf')) return 'PDF';
    if (mimeType?.includes('excel')) return 'Excel';
    return 'Documento';
  };

  const renderFolder = (folder: GoogleDriveFolder, level = 0) => {
    const isExpanded = expandedFolders.has(folder.id);
    const content = folderContents.get(folder.id);
    const isSelected = selectedFolderId === folder.id;

    return (
      <div key={folder.id} className="space-y-1">
        <div className="flex items-center space-x-2">
          <Collapsible>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={() => toggleFolder(folder.id)}
              >
                {isExpanded ? (
                  <ChevronDown className="h-4 w-4" />
                ) : (
                  <ChevronRight className="h-4 w-4" />
                )}
              </Button>
            </CollapsibleTrigger>
          </Collapsible>
          <Button
            variant={isSelected ? 'secondary' : 'ghost'}
            size="sm"
            className="flex-1 justify-start"
            onClick={() => handleFolderClick(folder.id, folder.name)}
            style={{ paddingLeft: `${level * 20 + 8}px` }}
          >
            <Folder className="mr-2 h-4 w-4 text-yellow-600" />
            {folder.name}
          </Button>
        </div>

        {isExpanded && content && (
          <div className="ml-4 space-y-1">
            {content.folders.map((subfolder) =>
              renderFolder(subfolder, level + 1),
            )}
            {content.files.map((file) => (
              <Button
                key={file.id}
                variant={selectedFileId === file.id ? 'secondary' : 'ghost'}
                size="sm"
                className="w-full justify-start"
                onClick={() => handleFileClick(file.id, file.name)}
                style={{ paddingLeft: `${(level + 1) * 20 + 8}px` }}
              >
                {getFileIcon(file.mimeType)}
                <span className="ml-2 flex-1 truncate text-left">
                  {file.name}
                </span>
                <Badge variant="outline" className="ml-2 text-xs">
                  {getFileTypeLabel(file.mimeType)}
                </Badge>
              </Button>
            ))}
          </div>
        )}
      </div>
    );
  };

  if (isPending) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Folder className="h-5 w-5" />
            <span>Filtros de Google Drive</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Cargando contenido...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <Folder className="h-5 w-5" />
          <span>Filtros de Google Drive</span>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        <div className="space-y-1">
          <h4 className="text-sm font-medium text-gray-700">Carpetas</h4>
          {folders.length === 0 ? (
            <p className="text-sm text-gray-500">No hay carpetas disponibles</p>
          ) : (
            <div className="space-y-1">
              {folders.map((folder) => renderFolder(folder))}
            </div>
          )}
        </div>

        {files.length > 0 && (
          <div className="space-y-1">
            <h4 className="text-sm font-medium text-gray-700">Documentos</h4>
            <div className="space-y-1">
              {files.map((file) => (
                <Button
                  key={file.id}
                  variant={selectedFileId === file.id ? 'secondary' : 'ghost'}
                  size="sm"
                  className="w-full justify-start"
                  onClick={() => handleFileClick(file.id, file.name)}
                >
                  {getFileIcon(file.mimeType)}
                  <span className="ml-2 flex-1 truncate text-left">
                    {file.name}
                  </span>
                  <Badge variant="outline" className="ml-2 text-xs">
                    {getFileTypeLabel(file.mimeType)}
                  </Badge>
                </Button>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
