import { getAllCreditors } from '@/features/creditor/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';

import { CreditorsPageClient } from './creditors-page-client';

export default async function CreditorsPage() {
  const [creditors] = await getAllCreditors();

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <CreditorsPageClient creditors={creditors || []} />
      </main>
    </div>
  );
}
