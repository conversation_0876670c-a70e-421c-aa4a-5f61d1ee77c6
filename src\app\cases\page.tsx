import { getAllCases, getCaseStats } from '@/features/case/actions';

import { CasesPageClient } from './components/cases-page-client';

export default async function CasesPage() {
  const [[cases], [stats]] = await Promise.all([getAllCases(), getCaseStats()]);

  return (
    <CasesPageClient
      initialCases={cases || []}
      initialStats={
        stats || {
          total: 0,
          byStatus: [],
          byType: [],
          negotiation: 0,
          agreementApproved: 0,
          totalDebt: 0,
        }
      }
    />
  );
}
