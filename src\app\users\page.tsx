import { Shield, Users, User<PERSON>heck, Setting<PERSON> } from 'lucide-react';

import { Card, CardContent } from '@/components/ui/card';
import { getAllRoles } from '@/features/role/actions';
import { getAllUsers } from '@/features/user/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';

import { UsersPageClient } from './users-page-client';

interface PageProps {
  searchParams: Promise<{
    search?: string;
    role?: string;
    tab?: string;
  }>;
}

export default async function UsersPage({ searchParams }: Readonly<PageProps>) {
  const params = await searchParams;
  const tab = params.tab ?? 'users';

  const [[users, usersErr], [roles, rolesErr]] = await Promise.all([
    getAllUsers(),
    getAllRoles(),
  ]);

  if (usersErr) {
    throw new Error('Failed to load users');
  }

  if (rolesErr) {
    throw new Error('Failed to load roles');
  }

  const activeUsersCount = users.filter((u) => u.status === 'Activo').length;
  const operatorsCount = users.filter(
    (u) => u.role.name === 'Operadora de Insolvencia',
  ).length;
  const totalCases = users.reduce((sum, u) => sum + u.assignedCases.length, 0);

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <div className="space-y-6">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-4">
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Total Usuarios
                    </p>
                    <p className="text-2xl font-bold">{users.length}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Usuarios Activos
                    </p>
                    <p className="text-2xl font-bold">{activeUsersCount}</p>
                  </div>
                  <UserCheck className="h-8 w-8 text-green-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Operadores
                    </p>
                    <p className="text-2xl font-bold">{operatorsCount}</p>
                  </div>
                  <Shield className="h-8 w-8 text-red-600" />
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardContent>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      Casos Asignados
                    </p>
                    <p className="text-2xl font-bold">{totalCases}</p>
                  </div>
                  <Settings className="h-8 w-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          <UsersPageClient
            initialUsers={users}
            roles={roles}
            initialTab={tab}
            searchParams={params}
          />
        </div>
      </main>
    </div>
  );
}
