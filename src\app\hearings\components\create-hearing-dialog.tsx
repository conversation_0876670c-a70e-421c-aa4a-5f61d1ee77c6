'use client';

import { Calendar, Clock, Video, Users } from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';

interface CreateHearingDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function CreateHearingDialog({
  open,
  onOpenChange,
}: Readonly<CreateHearingDialogProps>) {
  const [formData, setFormData] = useState({
    caseId: '',
    hearingType: '',
    date: '',
    time: '',
    zoomId: '',
    zoomPassword: '',
    notes: '',
    operator: '<PERSON><PERSON>',
  });

  const cases = [
    {
      id: 'INS-2025-001',
      debtorName: 'María González Pérez',
      type: 'Insolvencia',
    },
    {
      id: 'CON-2025-002',
      debtorName: 'Carlos Rodríguez Silva',
      type: 'Conciliación',
    },
    {
      id: 'ACU-2025-003',
      debtorName: 'Ana Martínez López',
      type: 'Acuerdo de Apoyo',
    },
  ];

  const hearingTypes = [
    'Negociación de Deudas',
    'Conciliación',
    'Seguimiento de Acuerdo',
    'Reforma de Acuerdo',
    'Calificación y Graduación',
  ];

  const generateZoomCredentials = () => {
    const zoomId = '123 456 7890';
    const password = 'password123';

    setFormData((prev) => ({
      ...prev,
      zoomId: zoomId,
      zoomPassword: password,
    }));

    toast.success('Se han generado las credenciales de Zoom automáticamente.');
  };

  const validateForm = () => {
    if (
      !formData.caseId ||
      !formData.hearingType ||
      !formData.date ||
      !formData.time
    ) {
      toast.error('Por favor complete todos los campos obligatorios');
      return false;
    }

    return true;
  };

  const handleSubmit = () => {
    if (!validateForm()) return;

    toast.success(
      `Audiencia para el caso ${formData.caseId} programada exitosamente.`,
    );

    onOpenChange(false);

    setFormData({
      caseId: '',
      hearingType: '',
      date: '',
      time: '',
      zoomId: '',
      zoomPassword: '',
      notes: '',
      operator: 'Beatriz Helena Malavera',
    });
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Programar Nueva Audiencia</DialogTitle>
          <DialogDescription>
            Configure los detalles para programar una nueva audiencia virtual
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="caseId">Caso *</Label>
              <Select
                value={formData.caseId}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, caseId: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar caso" />
                </SelectTrigger>
                <SelectContent>
                  {cases.map((case_) => (
                    <SelectItem key={case_.id} value={case_.id}>
                      {case_.id} - {case_.debtorName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="hearingType">Tipo de Audiencia *</Label>
              <Select
                value={formData.hearingType}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, hearingType: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar tipo" />
                </SelectTrigger>
                <SelectContent>
                  {hearingTypes.map((type) => (
                    <SelectItem key={type} value={type}>
                      {type}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="date">Fecha *</Label>
              <div className="relative">
                <Calendar className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, date: e.target.value }))
                  }
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="time">Hora *</Label>
              <div className="relative">
                <Clock className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
                <Input
                  id="time"
                  type="time"
                  value={formData.time}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, time: e.target.value }))
                  }
                  className="pl-10"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4 rounded-lg border bg-blue-50 p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Video className="h-5 w-5 text-blue-600" />
                <h3 className="font-medium">Configuración Zoom</h3>
              </div>
              <Button
                onClick={generateZoomCredentials}
                variant="outline"
                size="sm"
              >
                Generar Credenciales
              </Button>
            </div>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="zoomId">ID de Reunión</Label>
                <Input
                  id="zoomId"
                  placeholder="123 456 7890"
                  value={formData.zoomId}
                  onChange={(e) =>
                    setFormData((prev) => ({ ...prev, zoomId: e.target.value }))
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="zoomPassword">Código de Acceso</Label>
                <Input
                  id="zoomPassword"
                  placeholder="password123"
                  value={formData.zoomPassword}
                  onChange={(e) =>
                    setFormData((prev) => ({
                      ...prev,
                      zoomPassword: e.target.value,
                    }))
                  }
                />
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="operator">Operador Asignado</Label>
            <div className="relative">
              <Users className="absolute top-3 left-3 h-4 w-4 text-gray-400" />
              <Input
                id="operator"
                value={formData.operator}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, operator: e.target.value }))
                }
                className="pl-10"
                readOnly
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notas Adicionales</Label>
            <Textarea
              id="notes"
              placeholder="Observaciones o instrucciones especiales para la audiencia..."
              value={formData.notes}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, notes: e.target.value }))
              }
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <DialogClose asChild>
            <Button variant="outline">Cancelar</Button>
          </DialogClose>
          <Button onClick={handleSubmit}>Programar Audiencia</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
