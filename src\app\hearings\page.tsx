import { getAllHearings, getHearingStats } from '@/features/hearing/actions';

import { DashboardHeader } from '../dashboard/components/dashboard-header';

import { HearingsPageClient } from './hearings-page-client';

export default async function HearingsPage() {
  const [hearingsResult, statsResult] = await Promise.all([
    getAllHearings({}),
    getHearingStats(),
  ]);

  const hearingsData = hearingsResult[0] || [];
  const statsData = statsResult[0] || {
    total: 0,
    thisWeek: 0,
    thisMonth: 0,
    upcoming: 0,
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <DashboardHeader />

      <main className="container mx-auto px-4 py-6">
        <HearingsPageClient
          initialHearings={hearingsData}
          initialStats={statsData}
        />
      </main>
    </div>
  );
}
