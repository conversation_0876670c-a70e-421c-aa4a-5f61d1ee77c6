'use client';

import { FileText } from 'lucide-react';

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

interface DocumentTemplateCardProps {
  id: string;
  name: string;
  category: string;
  selected: boolean;
  onClick: (id: string) => void;
  categoryColor: string;
}

export function DocumentTemplateCard({
  id,
  name,
  category,
  selected,
  onClick,
  categoryColor,
}: Readonly<DocumentTemplateCardProps>) {
  return (
    <Card
      className={`h-full cursor-pointer transition-all ${
        selected ? 'bg-blue-50 ring-2 ring-blue-500' : 'hover:shadow-md'
      }`}
      onClick={() => onClick(id)}
    >
      <CardContent>
        <div className="mb-2 flex items-center justify-between">
          <Badge className={categoryColor}>{category}</Badge>
          <FileText className="h-5 w-5 text-blue-600" />
        </div>
        <h3 className="mb-1 font-medium">{name}</h3>
        <p className="text-sm text-gray-600">
          Genera un documento de {name.toLowerCase()} para un caso.
        </p>
      </CardContent>
    </Card>
  );
}
