'use client';

import { useState, useRef, useEffect } from 'react';
import { Editor } from '@tinymce/tinymce-react';
import { Save, Undo, Download, Eye } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

import { DocumentEditorProps } from '@/features/document/types';

export function DocumentEditor({
  documentId,
  initialContent = '',
  onSave,
  readOnly = false,
}: Readonly<DocumentEditorProps>) {
  const [content, setContent] = useState(initialContent);
  const [isSaving, setIsSaving] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [showSaveDialog, setShowSaveDialog] = useState(false);
  const [saveChanges, setSaveChanges] = useState('');
  const editorRef = useRef<unknown>(null);

  useEffect(() => {
    setContent(initialContent);
  }, [initialContent]);

  const handleEditorChange = (newContent: string) => {
    setContent(newContent);
    setHasChanges(newContent !== initialContent);
  };

  const handleSave = async () => {
    if (!hasChanges) return;

    setIsSaving(true);
    try {
      await onSave(content, saveChanges);
      setHasChanges(false);
      setSaveChanges('');
      setShowSaveDialog(false);
    } catch (error) {
      console.error('Error guardando documento:', error);
      alert('Error al guardar el documento');
    } finally {
      setIsSaving(false);
    }
  };

  const handleDownload = () => {
    // Implementar descarga del documento como Word
    const blob = new Blob([content], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `documento-${documentId}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  const handlePreview = () => {
    const previewWindow = window.open('', '_blank');
    if (previewWindow) {
      previewWindow.document.write(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Vista Previa del Documento</title>
            <style>
              body { 
                font-family: Arial, sans-serif; 
                max-width: 800px; 
                margin: 0 auto; 
                padding: 20px; 
                line-height: 1.6;
              }
              @media print {
                body { margin: 0; padding: 15mm; }
              }
            </style>
          </head>
          <body>
            ${content}
          </body>
        </html>
      `);
      previewWindow.document.close();
    }
  };

  return (
    <div className="space-y-4">
      {/* Barra de herramientas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Editor de Documentos</span>
            <div className="flex items-center space-x-2">
              {hasChanges && (
                <span className="text-sm text-orange-600">
                  Cambios sin guardar
                </span>
              )}
              <Button variant="outline" size="sm" onClick={handlePreview}>
                <Eye className="mr-2 h-4 w-4" />
                Vista Previa
              </Button>
              <Button variant="outline" size="sm" onClick={handleDownload}>
                <Download className="mr-2 h-4 w-4" />
                Descargar
              </Button>
              {!readOnly && (
                <Button
                  onClick={() => setShowSaveDialog(true)}
                  disabled={!hasChanges || isSaving}
                  className="flex items-center space-x-2"
                >
                  {isSaving ? (
                    <>
                      <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-white"></div>
                      <span>Guardando...</span>
                    </>
                  ) : (
                    <>
                      <Save className="h-4 w-4" />
                      <span>Guardar</span>
                    </>
                  )}
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Editor */}
      <Card>
        <CardContent className="p-0">
          <Editor
            apiKey="your-tinymce-api-key" // Reemplazar con tu API key de TinyMCE
            onInit={(_evt, editor) => (editorRef.current = editor)}
            value={content}
            onEditorChange={handleEditorChange}
            disabled={readOnly}
            init={{
              height: 600,
              menubar: !readOnly,
              plugins: [
                'advlist',
                'autolink',
                'lists',
                'link',
                'image',
                'charmap',
                'preview',
                'anchor',
                'searchreplace',
                'visualblocks',
                'code',
                'fullscreen',
                'insertdatetime',
                'media',
                'table',
                'code',
                'help',
                'wordcount',
                'pagebreak',
                'nonbreaking',
                'template',
                'textpattern',
              ],
              toolbar: readOnly
                ? false
                : 'undo redo | blocks | ' +
                  'bold italic forecolor | alignleft aligncenter ' +
                  'alignright alignjustify | bullist numlist outdent indent | ' +
                  'removeformat | table | pagebreak | help',
              content_style: `
                body { 
                  font-family: Arial, sans-serif; 
                  font-size: 14px; 
                  line-height: 1.6;
                  max-width: 800px;
                  margin: 0 auto;
                  padding: 20px;
                }
                @page {
                  margin: 2.5cm;
                }
              `,
              language: 'es',
              branding: false,
              resize: false,
              statusbar: !readOnly,

              setup: (editor: unknown) => {
                const editorInstance = editor as {
                  on: (event: string, callback: () => void) => void;
                  getBody: () => { style: { backgroundColor: string } };
                };
                editorInstance.on('init', () => {
                  if (readOnly) {
                    editorInstance.getBody().style.backgroundColor = '#f8f9fa';
                  }
                });
              },
            }}
          />
        </CardContent>
      </Card>

      {/* Diálogo de guardar */}
      <Dialog open={showSaveDialog} onOpenChange={setShowSaveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Guardar Cambios</DialogTitle>
            <DialogDescription>
              Describa los cambios realizados en esta versión del documento
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4">
            <div>
              <Label htmlFor="changes">Descripción de Cambios</Label>
              <Textarea
                id="changes"
                placeholder="Ej: Actualización de fechas, corrección de datos del deudor..."
                value={saveChanges}
                onChange={(e) => setSaveChanges(e.target.value)}
                rows={3}
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3">
            <Button variant="outline" onClick={() => setShowSaveDialog(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSave} disabled={isSaving}>
              {isSaving ? 'Guardando...' : 'Guardar'}
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Componente para mostrar el historial de versiones
interface DocumentVersionHistoryProps {
  documentId: string;
  versions: Array<{
    id: string;
    version: number;
    changes: string;
    createdAt: Date;
    createdBy: string;
  }>;
  onRestoreVersion: (versionId: string) => void;
}

export function DocumentVersionHistory({
  versions,
  onRestoreVersion,
}: Readonly<Omit<DocumentVersionHistoryProps, 'documentId'>>) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Historial de Versiones</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {versions.map((version) => (
            <div
              key={version.id}
              className="flex items-center justify-between rounded-lg border p-3"
            >
              <div>
                <div className="font-medium">Versión {version.version}</div>
                <div className="text-sm text-gray-600">
                  {version.changes || 'Sin descripción'}
                </div>
                <div className="text-xs text-gray-500">
                  {version.createdAt.toLocaleString('es-CO')} por{' '}
                  {version.createdBy}
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => onRestoreVersion(version.id)}
              >
                <Undo className="mr-2 h-4 w-4" />
                Restaurar
              </Button>
            </div>
          ))}

          {versions.length === 0 && (
            <div className="py-8 text-center text-gray-500">
              No hay versiones anteriores disponibles
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
