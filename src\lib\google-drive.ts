import { google } from 'googleapis';
import { Readable } from 'stream';

class GoogleDriveService {
  private drive;
  private auth;

  constructor() {
    this.auth = new google.auth.OAuth2(
      process.env.GOOGLE_DRIVE_CLIENT_ID,
      process.env.GOOGLE_DRIVE_CLIENT_SECRET,
      process.env.GOOGLE_DRIVE_REDIRECT_URI,
    );

    this.auth.setCredentials({
      refresh_token: process.env.GOOGLE_DRIVE_REFRESH_TOKEN,
    });

    this.drive = google.drive({ version: 'v3', auth: this.auth });
  }

  async uploadFile(
    fileName: string,
    fileBuffer: Buffer,
    mimeType: string,
    folderId?: string,
  ): Promise<string> {
    try {
      const fileMetadata = {
        name: fileName,
        parents: folderId ? [folderId] : [process.env.GOOGLE_DRIVE_FOLDER_ID!],
      };

      const media = {
        mimeType,
        body: Readable.from(fileBuffer),
      };

      const response = await this.drive.files.create({
        requestBody: fileMetadata,
        media,
        fields: 'id',
      });

      if (!response.data.id) {
        throw new Error('No se pudo obtener el ID del archivo subido');
      }

      return response.data.id;
    } catch (error) {
      console.error('Error subiendo archivo a Google Drive:', error);
      throw new Error('Error al subir archivo a Google Drive');
    }
  }

  async downloadFile(fileId: string): Promise<Buffer> {
    try {
      const response = await this.drive.files.get(
        {
          fileId,
          alt: 'media',
        },
        { responseType: 'stream' },
      );

      if (!response.data) {
        throw new Error('No se pudo descargar el archivo');
      }

      // Handle stream response
      const chunks: Buffer[] = [];
      const stream = response.data;

      return new Promise<Buffer>((resolve, reject) => {
        stream.on('data', (chunk: Buffer) => {
          chunks.push(chunk);
        });

        stream.on('end', () => {
          resolve(Buffer.concat(chunks));
        });

        stream.on('error', (error: Error) => {
          reject(error);
        });
      });
    } catch (error) {
      console.error('Error descargando archivo de Google Drive:', error);
      throw new Error('Error al descargar archivo de Google Drive');
    }
  }

  async deleteFile(fileId: string): Promise<void> {
    try {
      await this.drive.files.delete({
        fileId,
      });
    } catch (error) {
      console.error('Error eliminando archivo de Google Drive:', error);
      throw new Error('Error al eliminar archivo de Google Drive');
    }
  }

  async getFileInfo(fileId: string) {
    try {
      const response = await this.drive.files.get({
        fileId,
        fields: 'id,name,mimeType,size,createdTime,modifiedTime',
      });

      return response.data;
    } catch (error) {
      console.error('Error obteniendo información del archivo:', error);
      throw new Error('Error al obtener información del archivo');
    }
  }

  async createFolder(name: string, parentFolderId?: string): Promise<string> {
    try {
      const fileMetadata = {
        name,
        mimeType: 'application/vnd.google-apps.folder',
        parents: parentFolderId
          ? [parentFolderId]
          : [process.env.GOOGLE_DRIVE_FOLDER_ID!],
      };

      const response = await this.drive.files.create({
        requestBody: fileMetadata,
        fields: 'id',
      });

      if (!response.data.id) {
        throw new Error('No se pudo crear la carpeta');
      }

      return response.data.id;
    } catch (error) {
      console.error('Error creando carpeta en Google Drive:', error);
      throw new Error('Error al crear carpeta en Google Drive');
    }
  }

  async updateFile(
    fileId: string,
    fileName: string,
    fileBuffer: Buffer,
    mimeType: string,
  ): Promise<void> {
    try {
      const fileMetadata = {
        name: fileName,
      };

      const media = {
        mimeType,
        body: Readable.from(fileBuffer),
      };

      await this.drive.files.update({
        fileId,
        requestBody: fileMetadata,
        media,
      });
    } catch (error) {
      console.error('Error actualizando archivo en Google Drive:', error);
      throw new Error('Error al actualizar archivo en Google Drive');
    }
  }

  getFileUrl(fileId: string): string {
    return `https://drive.google.com/file/d/${fileId}/view`;
  }

  getDownloadUrl(fileId: string): string {
    return `https://drive.google.com/uc?export=download&id=${fileId}`;
  }

  async listFoldersAndFiles(parentFolderId?: string) {
    try {
      const folderId = parentFolderId || process.env.GOOGLE_DRIVE_FOLDER_ID!;

      // Get folders
      const foldersResponse = await this.drive.files.list({
        q: `'${folderId}' in parents and mimeType='application/vnd.google-apps.folder' and trashed=false`,
        fields: 'files(id,name,createdTime,modifiedTime)',
        orderBy: 'name',
      });

      // Get files (documents)
      const filesResponse = await this.drive.files.list({
        q: `'${folderId}' in parents and mimeType!='application/vnd.google-apps.folder' and trashed=false`,
        fields: 'files(id,name,mimeType,size,createdTime,modifiedTime,parents)',
        orderBy: 'name',
      });

      return {
        folders: foldersResponse.data.files || [],
        files: filesResponse.data.files || [],
      };
    } catch (error) {
      console.error(
        'Error listando carpetas y archivos de Google Drive:',
        error,
      );
      throw new Error('Error al listar contenido de Google Drive');
    }
  }

  async getFolderPath(folderId: string): Promise<string[]> {
    try {
      const path: string[] = [];
      let currentFolderId = folderId;
      const rootFolderId = process.env.GOOGLE_DRIVE_FOLDER_ID!;

      while (currentFolderId && currentFolderId !== rootFolderId) {
        const response = await this.drive.files.get({
          fileId: currentFolderId,
          fields: 'name,parents',
        });

        if (response.data.name) {
          path.unshift(response.data.name);
        }

        if (response.data.parents && response.data.parents.length > 0) {
          currentFolderId = response.data.parents[0];
        } else {
          break;
        }
      }

      return path;
    } catch (error) {
      console.error('Error obteniendo ruta de carpeta:', error);
      return [];
    }
  }
}

export const googleDriveService = new GoogleDriveService();
