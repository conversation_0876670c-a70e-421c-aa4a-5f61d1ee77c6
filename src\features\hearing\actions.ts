'use server';

import { revalidatePath } from 'next/cache';
import { createServerAction } from 'zsa';

import prisma from '@/lib/prisma';

import {
  scheduleHearingSchema,
  rescheduleHearingSchema,
  cancelHearingSchema,
  caseWithHearingSchema,
  hearingFilterSchema,
  hearingStatsSchema,
  getUpcomingHearingsSchema,
  getAllHearingsSchema,
  getTodayHearingsSchema,
  getHearingsByDateRangeSchema,
  getOperatorHearingsSchema,
  getHearingsByDateRangeInputSchema,
  getOperatorHearingsInputSchema,
} from './schemas';

export const getUpcomingHearings = createServerAction()
  .input(hearingFilterSchema.optional())
  .output(getUpcomingHearingsSchema)
  .handler(async ({ input: filters }) => {
    return prisma.case.findMany({
      where: {
        hearingDate:
          filters?.startDate && filters?.endDate
            ? {
                gte: new Date(filters.startDate),
                lte: new Date(filters.endDate),
              }
            : {
                gte: new Date(),
              },
        ...(filters?.operatorId && { operatorId: filters.operatorId }),
        ...(filters?.caseType && { type: filters.caseType }),
        ...(filters?.status && { status: filters.status }),
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        hearingDate: 'asc',
      },
    });
  });

export const getTodayHearings = createServerAction()
  .output(getTodayHearingsSchema)
  .handler(async () => {
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));

    return prisma.case.findMany({
      where: {
        hearingDate: {
          gte: startOfDay,
          lte: endOfDay,
        },
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        hearingDate: 'asc',
      },
    });
  });

export const getAllHearings = createServerAction()
  .input(hearingFilterSchema.optional())
  .output(getAllHearingsSchema)
  .handler(async ({ input: filters }) => {
    return prisma.case.findMany({
      where: {
        hearingDate:
          filters?.startDate && filters?.endDate
            ? {
                gte: new Date(filters.startDate),
                lte: new Date(filters.endDate),
              }
            : {
                not: null,
              },
        ...(filters?.operatorId && { operatorId: filters.operatorId }),
        ...(filters?.caseType && { type: filters.caseType }),
        ...(filters?.status && { status: filters.status }),
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        hearingDate: 'asc',
      },
    });
  });

export const scheduleHearing = createServerAction()
  .input(scheduleHearingSchema)
  .output(caseWithHearingSchema)
  .handler(async ({ input: data }) => {
    const hearingDate = new Date(data.hearingDate);

    if (hearingDate <= new Date()) {
      throw new Error('La fecha de la audiencia debe ser futura');
    }

    const hearing = await prisma.case.update({
      where: { id: data.caseId },
      data: {
        hearingDate,
        status: 'HEARING_SCHEDULED',
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    revalidatePath('/hearings');
    return hearing;
  });

export const rescheduleHearing = createServerAction()
  .input(rescheduleHearingSchema)
  .output(caseWithHearingSchema)
  .handler(async ({ input: data }) => {
    const newHearingDate = new Date(data.newHearingDate);

    if (newHearingDate <= new Date()) {
      throw new Error('La nueva fecha de la audiencia debe ser futura');
    }

    const hearing = await prisma.case.update({
      where: { id: data.caseId },
      data: {
        hearingDate: newHearingDate,
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    revalidatePath('/hearings');
    return hearing;
  });

export const cancelHearing = createServerAction()
  .input(cancelHearingSchema)
  .output(caseWithHearingSchema)
  .handler(async ({ input: data }) => {
    const updatedCase = await prisma.case.update({
      where: { id: data.caseId },
      data: {
        hearingDate: null,
        status: 'NEGOTIATION',
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    });

    revalidatePath('/hearings');
    return updatedCase;
  });

export const getHearingStats = createServerAction()
  .output(hearingStatsSchema)
  .handler(async () => {
    const today = new Date();
    const startOfWeek = new Date(
      today.setDate(today.getDate() - today.getDay()),
    );
    const endOfWeek = new Date(
      today.setDate(today.getDate() - today.getDay() + 6),
    );
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0);

    const [total, thisWeek, thisMonth, upcoming] = await Promise.all([
      prisma.case.count({
        where: {
          hearingDate: { not: null },
        },
      }),
      prisma.case.count({
        where: {
          hearingDate: {
            gte: startOfWeek,
            lte: endOfWeek,
          },
        },
      }),
      prisma.case.count({
        where: {
          hearingDate: {
            gte: startOfMonth,
            lte: endOfMonth,
          },
        },
      }),
      prisma.case.count({
        where: {
          hearingDate: {
            gte: new Date(),
          },
        },
      }),
    ]);

    return {
      total,
      thisWeek,
      thisMonth,
      upcoming,
    };
  });

export const getHearingsByDateRange = createServerAction()
  .input(getHearingsByDateRangeInputSchema)
  .output(getHearingsByDateRangeSchema)
  .handler(async ({ input: { startDate, endDate } }) => {
    return prisma.case.findMany({
      where: {
        hearingDate: {
          gte: new Date(startDate),
          lte: new Date(endDate),
        },
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        hearingDate: 'asc',
      },
    });
  });

export const getOperatorHearings = createServerAction()
  .input(getOperatorHearingsInputSchema)
  .output(getOperatorHearingsSchema)
  .handler(async ({ input: { operatorId } }) => {
    return prisma.case.findMany({
      where: {
        hearingDate: {
          gte: new Date(),
        },
        operatorId,
      },
      include: {
        debtor: {
          select: {
            id: true,
            name: true,
            email: true,
            phone: true,
          },
        },
        operator: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
      orderBy: {
        hearingDate: 'asc',
      },
    });
  });
