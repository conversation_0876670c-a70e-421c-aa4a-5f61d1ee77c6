import { z } from 'zod';

const decimalToNumber = z.any().transform((val) => {
  if (val && typeof val === 'object' && 'toNumber' in val) {
    return val.toNumber();
  }
  return Number(val);
});

const mapCaseTypeToSpanish = z.string().transform((type) => {
  const typeMap: Record<string, string> = {
    INSOLVENCY: 'Insolvencia',
    CONCILIATION: 'Conciliación',
    SUPPORT_AGREEMENT: 'Acuerdo de Apoyo',
  };
  return typeMap[type] || type;
});

const mapCaseStatusToSpanish = z.string().transform((status) => {
  const statusMap: Record<string, string> = {
    NEGOTIATION: 'En negociación',
    HEARING_SCHEDULED: 'Audiencia programada',
    PENDING_DOCUMENTS: 'Documentos pendientes',
    AGREEMENT_APPROVED: 'Acuerdo aprobado',
    CLOSED: 'Cerra<PERSON>',
  };
  return statusMap[status] || status;
});

const debtorForHearingSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
  phone: z.string(),
});

const operatorForHearingSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string(),
});

export const caseWithHearingSchema = z.object({
  id: z.string(),
  caseNumber: z.string(),
  debtorName: z.string(),
  type: mapCaseTypeToSpanish,
  status: mapCaseStatusToSpanish,
  totalDebt: decimalToNumber,
  creditors: z.coerce.number(),
  createdDate: z.coerce.date(),
  hearingDate: z.coerce.date().nullable(),
  phase: z.string().nullable(),
  causes: z.array(z.string()),
  debtorId: z.string(),
  operatorId: z.string(),
  debtor: debtorForHearingSchema,
  operator: operatorForHearingSchema,
});

export type CaseWithHearing = z.infer<typeof caseWithHearingSchema>;

export const hearingFilterSchema = z.object({
  startDate: z.string().optional(),
  endDate: z.string().optional(),
  operatorId: z.string().optional(),
  caseType: z.string().optional(),
  status: z.string().optional(),
});

export type HearingFilter = z.infer<typeof hearingFilterSchema>;

export const hearingStatsSchema = z.object({
  total: z.coerce.number(),
  thisWeek: z.coerce.number(),
  thisMonth: z.coerce.number(),
  upcoming: z.coerce.number(),
});

export type HearingStats = z.infer<typeof hearingStatsSchema>;

export const scheduleHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  hearingDate: z.string().min(1, 'La fecha de audiencia es requerida'),
  notes: z.string().optional(),
});

export type ScheduleHearingData = z.infer<typeof scheduleHearingSchema>;

export const rescheduleHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  newHearingDate: z.string().min(1, 'La nueva fecha de audiencia es requerida'),
  reason: z.string().min(1, 'La razón es requerida'),
});

export type RescheduleHearingData = z.infer<typeof rescheduleHearingSchema>;

export const cancelHearingSchema = z.object({
  caseId: z.string().min(1, 'El ID del caso es requerido'),
  reason: z.string().min(1, 'La razón es requerida'),
});

export type CancelHearingData = z.infer<typeof cancelHearingSchema>;

export const getHearingsByDateRangeInputSchema = z.object({
  startDate: z.string().min(1, 'La fecha de inicio es requerida'),
  endDate: z.string().min(1, 'La fecha de fin es requerida'),
});

export const getOperatorHearingsInputSchema = z.object({
  operatorId: z.string().min(1, 'El ID del operador es requerido'),
});

export const getAllHearingsSchema = z.array(caseWithHearingSchema);
export const getUpcomingHearingsSchema = z.array(caseWithHearingSchema);
export const getTodayHearingsSchema = z.array(caseWithHearingSchema);
export const getHearingsByDateRangeSchema = z.array(caseWithHearingSchema);
export const getOperatorHearingsSchema = z.array(caseWithHearingSchema);
