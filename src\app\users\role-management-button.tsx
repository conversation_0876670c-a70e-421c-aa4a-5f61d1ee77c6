'use client';

import { UserPlus } from 'lucide-react';
import { useState } from 'react';

import { Button } from '@/components/ui/button';

import { CreateRoleDialog } from './components/create-role-dialog';

interface RoleManagementButtonProps {
  onRoleCreated?: () => void;
}

export function RoleManagementButton({
  onRoleCreated,
}: Readonly<RoleManagementButtonProps>) {
  const [showDialog, setShowDialog] = useState(false);

  return (
    <>
      <Button onClick={() => setShowDialog(true)}>
        <UserPlus className="mr-2 h-4 w-4" />
        Nuevo Rol
      </Button>
      <CreateRoleDialog
        open={showDialog}
        onOpenChange={setShowDialog}
        onRoleCreated={onRoleCreated}
      />
    </>
  );
}
