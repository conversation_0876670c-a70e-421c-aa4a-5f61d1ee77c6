'use client';

import { Edit, Users, Trash2 } from 'lucide-react';
import { useState } from 'react';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { getPermissionName } from '@/features/role/schemas';

import { DeleteRoleDialog } from './components/delete-role-dialog';
import { EditRoleDialog } from './components/edit-role-dialog';
import { RoleUsersDialog } from './components/role-users-dialog';

import type { User } from '@/features/user/schemas';
import type { Role } from '@/features/role/schemas';

interface RoleCardProps {
  role: Role;
  users: User[];
}

export function RoleCard({ role, users }: Readonly<RoleCardProps>) {
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [showRoleUsersDialog, setShowRoleUsersDialog] = useState(false);

  return (
    <>
      <Card className="border transition-shadow hover:shadow-lg">
        <CardContent>
          <div className="mb-4 flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold">{role.name}</h3>
              <p className="text-sm text-gray-600">{role.description}</p>
            </div>
            <div className="text-center">
              <Badge className={role.color}>{role.users.length}</Badge>
              <p className="mt-1 text-xs text-gray-500">usuarios</p>
            </div>
          </div>

          <div className="mb-4">
            <p className="mb-2 text-sm font-medium">Permisos:</p>
            <div className="max-h-32 overflow-y-auto">
              <ul className="space-y-1 text-sm text-gray-600">
                {role.permissions
                  .slice(0, 5)
                  .map((permission: string, idx: number) => (
                    <li key={idx} className="flex items-center">
                      <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
                      {getPermissionName(permission)}
                    </li>
                  ))}
                {role.permissions.length > 5 && (
                  <li className="pl-4 text-xs text-gray-500">
                    +{role.permissions.length - 5} permisos más...
                  </li>
                )}
              </ul>
            </div>
          </div>

          <div className="flex flex-col gap-2 sm:flex-row">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowEditDialog(true)}
              className="flex-1"
            >
              <Edit className="mr-2 h-4 w-4" />
              Editar
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowRoleUsersDialog(true)}
              className="flex-1"
            >
              <Users className="mr-2 h-4 w-4" />
              Ver Usuarios
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowDeleteDialog(true)}
              disabled={role.users.length > 0}
              className="flex-1 hover:border-red-300 hover:bg-red-50 hover:text-red-600"
              title={
                role.users.length > 0
                  ? 'No se puede eliminar un rol con usuarios asignados'
                  : ''
              }
            >
              <Trash2 className="mr-2 h-4 w-4" />
              Eliminar
            </Button>
          </div>
        </CardContent>
      </Card>

      <EditRoleDialog
        open={showEditDialog}
        onOpenChange={setShowEditDialog}
        role={role}
      />

      <DeleteRoleDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        role={role}
      />

      <RoleUsersDialog
        open={showRoleUsersDialog}
        onOpenChange={setShowRoleUsersDialog}
        role={role}
        users={users}
      />
    </>
  );
}
