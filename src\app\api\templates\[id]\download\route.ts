import { NextRequest, NextResponse } from 'next/server';
import { downloadDocumentTemplate } from '@/features/document/actions';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> },
) {
  try {
    const { id } = await params;
    const [result] = await downloadDocumentTemplate({ id });

    if (!result) {
      return NextResponse.json(
        { error: 'Plantilla no encontrada' },
        { status: 404 },
      );
    }

    const { buffer, fileName, mimeType } = result;

    return new NextResponse(buffer, {
      headers: {
        'Content-Type': mimeType,
        'Content-Disposition': `inline; filename="${fileName}"`,
        'Cache-Control': 'public, max-age=3600',
      },
    });
  } catch (error) {
    console.error('Error descargando plantilla:', error);
    return NextResponse.json(
      { error: 'Error al descargar la plantilla' },
      { status: 500 },
    );
  }
}
