'use client';

import { useRef } from 'react';
import { useServerAction } from 'zsa-react';
import { toast } from 'sonner';
import { DocumentTemplate } from '@/features/document/types';
import { deleteDocumentTemplate } from '@/features/document/actions';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogClose,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';

interface DeleteTemplateDialogProps {
  template: DocumentTemplate | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onTemplateDeleted: (templateId: string) => void;
}

export function DeleteTemplateDialog({
  template,
  open,
  onOpenChange,
  onTemplateDeleted,
}: Readonly<DeleteTemplateDialogProps>) {
  const closeRef = useRef<HTMLButtonElement>(null);

  const { execute: executeDelete, isPending } = useServerAction(
    deleteDocumentTemplate,
    {
      onSuccess: () => {
        toast.success('Plantilla eliminada exitosamente');
        if (template) {
          onTemplateDeleted(template.id);
        }
        closeRef.current?.click();
      },
      onError: ({ err }) => {
        toast.error(err.message || 'Error al eliminar la plantilla');
      },
    },
  );

  const handleDelete = () => {
    if (template) {
      executeDelete({ id: template.id });
    }
  };

  if (!template) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Eliminar Plantilla</DialogTitle>
          <DialogDescription>
            ¿Estás seguro de que deseas eliminar la plantilla &quot;
            {template.fileName}&quot;? Esta acción no se puede deshacer.
          </DialogDescription>
        </DialogHeader>

        <div className="flex justify-end space-x-2">
          <DialogClose asChild>
            <Button
              type="button"
              variant="outline"
              ref={closeRef}
              disabled={isPending}
            >
              Cancelar
            </Button>
          </DialogClose>
          <Button
            type="button"
            variant="destructive"
            onClick={handleDelete}
            disabled={isPending}
          >
            {isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Eliminar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
